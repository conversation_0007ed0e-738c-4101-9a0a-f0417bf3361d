local function Make(color_r, color_g, color_b, scale)
    local assets = {}
    local function fn()
        local inst = CreateEntity()
        inst.entity:AddTransform()
        inst.entity:AddAnimState()
        inst.entity:AddNetwork()

        inst:AddTag("FX")
        inst.persists = false

        inst.AnimState:SetBank("stafflight")
        inst.AnimState:SetBuild("stafflight")
        inst.AnimState:PlayAnimation("idle_loop", true)
        local strength = (GetModConfigData("vfx_strength") or 1)
        local alpha = strength == 0 and 0.4 or (strength == 2 and 0.9 or 0.7)
        local sc = strength == 0 and 1.0 or (strength == 2 and 1.8 or 1.2)
        inst.AnimState:SetMultColour(color_r or 1, color_g or 1, color_b or 1, alpha)
        inst.Transform:SetScale(scale or sc, scale or sc, scale or sc)

        inst.entity:SetPristine()
        if not TheWorld.ismastersim then
            return inst
        end

        inst:DoTaskInTime(1.0, inst.Remove)
        return inst
    end
    return Prefab("fx_warning_circle", fn, assets)
end

return Make()
