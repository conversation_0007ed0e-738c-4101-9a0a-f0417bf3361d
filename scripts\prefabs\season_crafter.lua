local MakePlayerCharacter = require "prefabs/player_common"

-- 使用Wickerbottom的资源作为模板
local assets = {
    <PERSON><PERSON>("ANIM", "anim/player_basic.zip"),
    <PERSON><PERSON>("ANIM", "anim/player_idles_shiver.zip"),
    <PERSON><PERSON>("ANIM", "anim/player_actions_axe.zip"),
    <PERSON><PERSON>("ANIM", "anim/player_actions_pickaxe.zip"),
    <PERSON><PERSON>("ANIM", "anim/player_actions_shovel.zip"),
    <PERSON><PERSON>("ANIM", "anim/player_actions_blowdart.zip"),
    <PERSON><PERSON>("ANIM", "anim/player_actions_eat.zip"),
    <PERSON><PERSON>("ANIM", "anim/player_actions_item.zip"),
    <PERSON><PERSON>("ANIM", "anim/player_actions_uniqueitem.zip"),
    <PERSON><PERSON>("ANIM", "anim/player_actions_bugnet.zip"),
    <PERSON><PERSON>("ANIM", "anim/player_actions_fishing.zip"),
    <PERSON><PERSON>("ANIM", "anim/player_actions_boomerang.zip"),
    <PERSON><PERSON>("ANIM", "anim/player_bush_hat.zip"),
    <PERSON><PERSON>("ANIM", "anim/player_attacks.zip"),
    <PERSON><PERSON>("ANIM", "anim/player_idles.zip"),
    Asset("ANIM", "anim/player_rebirth.zip"),
    Asset("ANIM", "anim/player_jump.zip"),
    Asset("ANIM", "anim/player_amulet_resurrect.zip"),
    Asset("ANIM", "anim/player_teleport.zip"),
    Asset("ANIM", "anim/wilson_fx.zip"),
    Asset("ANIM", "anim/player_one_man_band.zip"),
    Asset("ANIM", "anim/shadow_hands.zip"),
    Asset("SOUND", "sound/sfx.fsb"),
    Asset("SOUND", "sound/wilson.fsb"),
    Asset("ANIM", "anim/beard.zip"),

    -- Wickerbottom特有资源
    Asset("ANIM", "anim/wickerbottom.zip"),
    Asset("SOUND", "sound/wickerbottom.fsb"),
}

local prefabs = {}

-- 初始物品：给季匠一些基础的季节相关物品
local start_inv = {
    "season_shard_spring",
    "season_shard_summer",
    "season_shard_autumn",
    "season_shard_winter",
}

-- 引用全局函数
local SpawnPrefab = GLOBAL.SpawnPrefab
local TUNING = GLOBAL.TUNING

local function SeasonFX(inst, data)
    if not data or not data.season then return end

    -- 音效
    if inst.SoundEmitter then
        inst.SoundEmitter:PlaySound("dontstarve/common/staff_coldlight")
    end

    -- 改色staffcastfx视觉效果
    local fx = SpawnPrefab("staff_castinglight")
    if fx then
        fx.Transform:SetPosition(inst.Transform:GetWorldPosition())

        -- 根据季节调整颜色
        local season = data.season
        if season == "spring" then
            fx.AnimState:SetMultColour(0.5, 1.0, 0.5, 0.8) -- 绿色
        elseif season == "summer" then
            fx.AnimState:SetMultColour(1.0, 0.6, 0.2, 0.8) -- 橙色
        elseif season == "autumn" then
            fx.AnimState:SetMultColour(0.8, 0.5, 0.2, 0.8) -- 褐色
        elseif season == "winter" then
            fx.AnimState:SetMultColour(0.6, 0.8, 1.0, 0.8) -- 蓝色
        end

        fx:DoTaskInTime(1.0, fx.Remove)
    end

    -- 角色提示文字
    if inst.components and inst.components.talker then
        local season_names = {
            spring = "春之刻印",
            summer = "夏之刻印",
            autumn = "秋之刻印",
            winter = "冬之刻印"
        }
        inst.components.talker:Say(season_names[data.season] or "季节刻印")
    end
end

local function common_postinit(inst)
    inst:AddTag("seasonal")
    -- 客户端用：最小化网络变量由组件同步
end

local function master_postinit(inst)
    -- 使用Wickerbottom的音效
    inst.soundsname = "wickerbottom"

    -- 基础属性
    inst.components.health:SetMaxHealth(150)
    inst.components.hunger:SetMax(150)
    inst.components.hunger:SetRate(TUNING.WILSON_HUNGER_RATE)
    inst.components.sanity:SetMax(200)

    -- 季节被动组件（完全负责所有季节效果）
    inst:AddComponent("season_engraving")
    inst:ListenForEvent("season_engraving_fx", SeasonFX)

    -- 冬季近战减速需要的攻击监听
    inst:ListenForEvent("onattackother", function(inst, data)
        if data and data.target and inst.components.season_engraving then
            local season = inst.components.season_engraving:GetSeason()
            if season == "winter" then
                -- 冰缓打击：给目标施加减速
                if not data.target.components.seasonal_debuff then
                    data.target:AddComponent("seasonal_debuff")
                end
                local is_boss = data.target:HasTag("epic")
                local mult = is_boss and (TUNING.SEASON_CRAFTER_WINTER_BOSS_SLOW_MULT or 0.85) or (TUNING.SEASON_CRAFTER_WINTER_SLOW_MULT or 0.8)
                local dur = is_boss and (TUNING.SEASON_CRAFTER_WINTER_BOSS_SLOW_DURATION or 1.5) or (TUNING.SEASON_CRAFTER_WINTER_SLOW_DURATION or 2.0)
                data.target.components.seasonal_debuff:ApplySlow(mult, dur, false)
            end
        end
    end)
end

return MakePlayerCharacter("season_crafter", prefabs, assets, common_postinit, master_postinit, start_inv)
