local _G = GLOBAL
local Class = _G.Class
local TheWorld = _G.TheWorld
local AllPlayers = _G.AllPlayers
local TUNING = _G.TUNING

local function IsValidPlayer(p)
    return p and p:<PERSON>Valid() and p:<PERSON><PERSON><PERSON>("player") and not p:<PERSON><PERSON><PERSON>("playerghost")
end

local SeasonWardenInvasion = Class(function(self, inst)
    self.marker = nil
    self.inst = inst
    self.enabled = (_G.GetModConfigData("invasion_enabled") ~= false)
    self.per_season = TUNING.SEASON_WARDEN_INVASIONS_PER_SEASON or 2
    self.respawn_days = TUNING.SEASON_WARDEN_INVASION_RESPAWN_DAYS or 2
    self.warn_secs = TUNING.SEASON_WARDEN_INVASION_WARN_SECS or 5
    self.battle_radius = TUNING.SEASON_WARDEN_BATTLE_RADIUS or 30
    self.hp_mul = TUNING.SEASON_WARDEN_INVASION_HP_MUL or 0.4
    self.loot_mul = TUNING.SEASON_WARDEN_INVASION_LOOT_MUL or 0.5

    self.invasions_done = 0
    self.active = false
    self.boss = nil
    self.origin = nil

    if not TheWorld.ismastersim then
        return
    end

    inst:WatchWorldState("season", function() self:OnSeasonChanged() end)
    inst:DoTaskInTime(0, function() self:OnSeasonChanged(true) end)
end)

function SeasonWardenInvasion:OnSeasonChanged(initial)
    if not self.enabled then return end
    -- 重置计数，清理进行中的入侵
    self.invasions_done = 0
    if self.boss and self.boss:IsValid() then
        self.boss:Remove()
    end
    if self.marker and self.marker:IsValid() then
        self.marker:Remove()
    end
    self.marker = nil
    self.active = false
    self.boss = nil
    self.origin = nil

    -- 调度第一次入侵（随机0.5~1.5天后）
    local total = TUNING.TOTAL_DAY_TIME or 480
    local delay = total * (0.5 + math.random())
    self.inst:DoTaskInTime(delay, function() self:TryStartInvasion() end)
end

function SeasonWardenInvasion:TryStartInvasion()
    if not self.enabled or self.active then return end
    if self.invasions_done >= self.per_season then return end
    -- 选择随机玩家
    local candidates = {}
    for _,p in ipairs(AllPlayers or {}) do if IsValidPlayer(p) then table.insert(candidates, p) end end
    if #candidates == 0 then return end
    local player = candidates[math.random(#candidates)]
    local pos = player:GetPosition()
    local offset = FindWalkableOffset(pos, math.random()*2*PI, math.random(8,12), 16, true)
    local spawnpt = offset and (pos + offset) or pos
    -- 预警FX
    local fx = SpawnPrefab("staff_castinglight")
    if fx then fx.Transform:SetPosition(spawnpt.x, 0, spawnpt.z); fx:DoTaskInTime(self.warn_secs, fx.Remove) end
    -- 临时小地图标记（复用firepit图标，warn_secs后清除）
    if self.marker and self.marker:IsValid() then self.marker:Remove() end
    self.marker = SpawnPrefab("invasion_marker")
    if self.marker and self.marker.entity and self.marker.entity.AddMiniMapEntity then
        self.marker.entity:AddMiniMapEntity()
        self.marker.MiniMapEntity:SetIcon("firepit.png")
        self.marker.MiniMapEntity:SetPriority(1)
        self.marker.Transform:SetPosition(spawnpt.x, 0, spawnpt.z)
        self.inst:DoTaskInTime(self.warn_secs + 10, function()
            if self.marker and self.marker:IsValid() then self.marker:Remove() end
            self.marker = nil
        end)
    end
    self.active = true
    self.origin = {x=spawnpt.x, y=0, z=spawnpt.z}
    -- 地图标记（轻量占位）：给所有玩家发聊天提示与客户端小地图ping（复用内置 AddPing）
    if TheWorld.components.worldsettings_overrides == nil then
        TheNet:Announce("季Boss入侵预警！", nil, nil)
    end
    if TheWorld.minimap ~= nil and TheWorld.minimap.MiniMap ~= nil then
        TheWorld.minimap.MiniMap:ShowArea(spawnpt.x, 0, spawnpt.z, 15)
    end
    self.inst:DoTaskInTime(self.warn_secs, function() self:SpawnBossAt(spawnpt) end)
end

function SeasonWardenInvasion:SpawnBossAt(spawnpt)
    if not self.active then return end
    local boss = SpawnPrefab("boss_season_warden")
    if not boss then self.active = false; return end
    boss.Transform:SetPosition(spawnpt.x, 0, spawnpt.z)
    boss._invasion = true
    -- 标记切换为战斗态图标（复用 hound.png）
    if self.marker and self.marker:IsValid() and self.marker.MiniMapEntity then
        self.marker.MiniMapEntity:SetIcon("hound.png")
        self.marker.MiniMapEntity:SetPriority(1)
        local x,y,z = boss.Transform:GetWorldPosition()
        self.marker.Transform:SetPosition(x,0,z)
    end
    -- HP缩放
    if boss.components and boss.components.health then
        local base = TUNING.SEASON_BOSS_HP or 8000
        local mhp = math.max(1, math.floor(base * self.hp_mul))
        boss.components.health:SetMaxHealth(mhp)
        boss.components.health:SetPercent(1)
    end
    -- 结算：监听死亡（成功）与距离（失败）
    boss:ListenForEvent("death", function(inst)
        self:OnBossDeath(inst)
    end)
    boss:DoPeriodicTask(1, function()
        self:CheckRadius()
        if self.marker and self.marker:IsValid() then
            local x,y,z = boss.Transform:GetWorldPosition()
            self.marker.Transform:SetPosition(x,0,z)
        end
    end)
    -- 保底核心掉落（至少1个）
    boss:ListenForEvent("death", function(inst)
        local x,y,z = inst.Transform:GetWorldPosition()
        -- 掉落倍率：根据配置调整保底/概率（简化：核心*倍率四舍五入）
        local cores = math.max(1, math.floor((TUNING.SEASON_WARDEN_INVASION_LOOT_MUL or 0.5) * 2 + 0.5))
        for i=1,cores do
            local c = SpawnPrefab("season_core")
    -- Boss 存在时，标记跟随并持续存在
    if self.marker and self.marker:IsValid() then
        self.marker.Transform:SetPosition(spawnpt.x, 0, spawnpt.z)
        self.marker.MiniMapEntity:SetPriority(1)
        -- 不再自动清除，等待死亡/失败回调时移除
    end
            if c then c.Transform:SetPosition(x,0,z) end
        end
        -- TODO: 可扩展随机掉落表
    end)

    self.boss = boss
end

function SeasonWardenInvasion:CheckRadius()
    if not self.active or not self.boss or not self.boss:IsValid() then return end
    if not self.origin then return end
    local x,y,z = self.boss.Transform:GetWorldPosition()
    local dx = x - self.origin.x
    local dz = z - self.origin.z
    local d2 = dx*dx + dz*dz
    if d2 > (self.battle_radius * self.battle_radius) then
        -- 超出半径：结束这次入侵，标记为失败，安排2天后重试
        self.boss:Remove()
        self.boss = nil
        self.active = false
        self:ScheduleRetry()
    end
end

function SeasonWardenInvasion:OnBossDeath(inst)
    -- 本季完成次数+1
    self.invasions_done = self.invasions_done + 1
    if self.marker and self.marker:IsValid() then self.marker:Remove() end
    self.marker = nil
    self.active = false
    self.boss = nil
    self.origin = nil
    -- 若未达目标次数，随机延迟后继续下一次入侵
    if self.invasions_done < self.per_season then
        local total = TUNING.TOTAL_DAY_TIME or 480
        self.inst:DoTaskInTime(total * (0.5 + math.random()), function() self:TryStartInvasion() end)
    end
end

function SeasonWardenInvasion:ScheduleRetry()
    local total = TUNING.TOTAL_DAY_TIME or 480
    local delay = total * (self.respawn_days or 2)
    self.inst:DoTaskInTime(delay, function() self:TryStartInvasion() end)
end

return SeasonWardenInvasion
