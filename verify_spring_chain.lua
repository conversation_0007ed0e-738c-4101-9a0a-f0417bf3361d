-- 验证春季电链跳跃伤害减半的脚本
-- 使用方法：在游戏控制台中执行 dofile("mods/thinking/verify_spring_chain.lua")

local function VerifySpringChain()
    local player = ThePlayer
    if not player then
        print("错误：找不到玩家")
        return
    end
    
    print("=== 验证春季电链跳跃伤害 ===")
    
    -- 切换到春季
    TheWorld:PushEvent("ms_setseason", "spring")
    if player.components.season_engraving then
        player.components.season_engraving:Refresh(true)
    end
    
    -- 生成季节之刃
    local blade = SpawnPrefab("season_blade")
    if not blade then
        print("错误：无法生成季节之刃")
        return
    end
    
    player.components.inventory:GiveItem(blade)
    player.components.inventory:Equip(blade)
    print("✓ 季节之刃已装备")
    
    -- 生成主目标
    local main_target = SpawnPrefab("pig")
    if main_target then
        local px, py, pz = player.Transform:GetWorldPosition()
        main_target.Transform:SetPosition(px + 2, py, pz)
        print("✓ 主目标已生成")
        
        -- 生成跳跃目标
        local chain_target1 = SpawnPrefab("pig")
        local chain_target2 = SpawnPrefab("pig")
        if chain_target1 then
            chain_target1.Transform:SetPosition(px + 3, py, pz + 1)
            print("✓ 电链目标1已生成")
        end
        if chain_target2 then
            chain_target2.Transform:SetPosition(px + 1, py, pz + 1)
            print("✓ 电链目标2已生成")
        end
        
        -- 记录初始血量
        local main_hp = main_target.components.health.currenthealth
        local chain1_hp = chain_target1 and chain_target1.components.health.currenthealth or 0
        local chain2_hp = chain_target2 and chain_target2.components.health.currenthealth or 0
        
        print("初始血量：")
        print("- 主目标：" .. main_hp)
        print("- 电链目标1：" .. chain1_hp)
        print("- 电链目标2：" .. chain2_hp)
        
        -- 连续攻击3次触发爆发
        for i = 1, 3 do
            if blade.components.weapon.onattack then
                blade.components.weapon.onattack(blade, player, main_target)
                print("第" .. i .. "次攻击完成")
            end
        end
        
        -- 等待一下让伤害生效
        player:DoTaskInTime(0.5, function()
            local main_hp_after = main_target.components.health.currenthealth
            local chain1_hp_after = chain_target1 and chain_target1.components.health.currenthealth or 0
            local chain2_hp_after = chain_target2 and chain_target2.components.health.currenthealth or 0
            
            print("\n攻击后血量：")
            print("- 主目标：" .. main_hp_after .. " (受到伤害：" .. (main_hp - main_hp_after) .. ")")
            print("- 电链目标1：" .. chain1_hp_after .. " (受到伤害：" .. (chain1_hp - chain1_hp_after) .. ")")
            print("- 电链目标2：" .. chain2_hp_after .. " (受到伤害：" .. (chain2_hp - chain2_hp_after) .. ")")
            
            print("\n预期结果：")
            print("- 主目标应受到基础伤害34 + 爆发伤害50 = 84点伤害")
            print("- 电链目标应受到爆发伤害25点伤害（50点的一半）")
            
            print("\n=== 验证完成 ===")
            print("请手动清理生成的物品和生物")
        end)
    end
end

-- 执行验证
VerifySpringChain()
