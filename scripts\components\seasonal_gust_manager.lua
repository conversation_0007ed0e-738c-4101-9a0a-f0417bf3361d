local _G = GLOBAL
local Class = _G.Class
local TheWorld = _G.TheWorld
local TUNING = _G.TUNING
local AllPlayers = _G.AllPlayers

local SeasonalGust = Class(function(self, inst)
    self.inst = inst
    self.enabled = true
    self.events_left = 0
    self.scheduled = {}

    if not TheWorld.ismastersim then
        return
    end

    inst:WatchWorldState("season", function()
        self:OnSeasonChanged()
    end)
    inst:DoTaskInTime(0, function() self:OnSeasonChanged(true) end)
end)

local function SpawnShardsNearPlayer(player)
    if not player or not player:IsValid() then return end
    local season = TheWorld.state.season or "autumn"
    local shard_pref = "season_shard_"..season
    local count = math.random(3,5)
    for i=1,count do
        local x,y,z = player.Transform:GetWorldPosition()
        local offset = FindWalkableOffset(player:GetPosition(), math.random()*2*PI, math.random(2,5), 8, true)
        if offset ~= nil then
            x = x + offset.x
            z = z + offset.z
        end
        local ent = SpawnPrefab(shard_pref)
        if ent then
            ent.Transform:SetPosition(x, 0, z)
        end
    end
end

function SeasonalGust:TriggerEvent()
    if not self.enabled then return end
    -- 事件：在每位玩家附近生成3~5个季节碎片
    for _, p in ipairs(AllPlayers or {}) do
        SpawnShardsNearPlayer(p)
    end
end

function SeasonalGust:OnSeasonChanged(initial)
    -- 读取配置：每季触发次数
    local per_season = _G.GetModConfigData("gust_frequency") or 2
    self.events_left = per_season
    -- 清理已有计划
    for _, t in ipairs(self.scheduled) do
        if t and t.Cancel then t:Cancel() end
    end
    self.scheduled = {}

    -- 计划在本季2次触发（简化：按天偏移），随机在当前季内的 [min,max] 天后触发
    local min_days = TUNING.SEASON_EVENT_MIN_DAYS or 8
    local max_days = TUNING.SEASON_EVENT_MAX_DAYS or 12
    local total_time = _G.TUNING.TOTAL_DAY_TIME or 480

    for i=1,per_season do
        local days = math.random(min_days, max_days)
        local delay = days * total_time * 0.25 -- 简化：缩短等待，加快测试节奏（1/4），后续可调为1.0
        local task = self.inst:DoTaskInTime(delay, function()
            self:TriggerEvent()
        end)
        table.insert(self.scheduled, task)
    end
end

return SeasonalGust
