-- 测试四种季芯的功能
-- 在游戏中运行：c_exec(loadfile("test_season_cores.lua")())

local function TestSeasonCores()
    print("=== 季芯系统测试 ===")
    
    local player = ThePlayer
    if not player then
        print("❌ 找不到玩家")
        return
    end
    
    -- 1. 测试四种季节碎片的生成和外观
    print("\n--- 测试季节碎片 ---")
    local seasons = {"spring", "summer", "autumn", "winter"}
    local season_names = {
        spring = "春季",
        summer = "夏季", 
        autumn = "秋季",
        winter = "冬季"
    }
    
    local shards = {}
    for _, season in ipairs(seasons) do
        local shard = SpawnPrefab("season_shard_" .. season)
        if shard then
            player.components.inventory:GiveItem(shard)
            shards[season] = shard
            print("✓ " .. season_names[season] .. "碎片已生成")
            
            -- 测试检查功能
            if shard.components.inspectable and shard.components.inspectable.getstatus then
                print("  检查信息：" .. shard.components.inspectable.getstatus(shard))
            end
        else
            print("❌ " .. season_names[season] .. "碎片生成失败")
        end
    end
    
    -- 2. 测试通用季芯合成（需要四种不同碎片各1个）
    print("\n--- 测试通用季芯合成 ---")
    if #shards == 4 then
        -- 模拟合成过程（实际游戏中通过工坊台）
        local generic_core = SpawnPrefab("season_core")
        if generic_core then
            player.components.inventory:GiveItem(generic_core)
            print("✓ 通用季芯合成成功")
            
            if generic_core.components.inspectable and generic_core.components.inspectable.getstatus then
                print("  检查信息：" .. generic_core.components.inspectable.getstatus(generic_core))
            end
        end
    end
    
    -- 3. 测试特定季节季芯
    print("\n--- 测试特定季节季芯 ---")
    for _, season in ipairs(seasons) do
        -- 生成4个同季节碎片
        local season_shards = {}
        for i = 1, 4 do
            local shard = SpawnPrefab("season_shard_" .. season)
            if shard then
                table.insert(season_shards, shard)
            end
        end
        
        if #season_shards == 4 then
            -- 模拟合成特定季芯
            local specific_core = SpawnPrefab("season_core_" .. season)
            if specific_core then
                player.components.inventory:GiveItem(specific_core)
                print("✓ " .. season_names[season] .. "季芯合成成功")
                
                if specific_core.components.inspectable and specific_core.components.inspectable.getstatus then
                    print("  检查信息：" .. specific_core.components.inspectable.getstatus(specific_core))
                end
                
                -- 测试季芯炸符制作
                local sigil = SpawnPrefab("season_sigil_" .. season)
                if sigil then
                    player.components.inventory:GiveItem(sigil)
                    print("✓ " .. season_names[season] .. "炸符制作成功")
                    
                    if sigil.components.inspectable and sigil.components.inspectable.getstatus then
                        print("  检查信息：" .. sigil.components.inspectable.getstatus(sigil))
                    end
                end
            end
            
            -- 清理测试用的碎片
            for _, shard in ipairs(season_shards) do
                if shard:IsValid() then
                    shard:Remove()
                end
            end
        end
    end
    
    -- 4. 测试祭坛召唤机制
    print("\n--- 测试祭坛召唤机制 ---")
    local altar = SpawnPrefab("season_altar")
    if altar then
        local x, y, z = player.Transform:GetWorldPosition()
        altar.Transform:SetPosition(x + 5, 0, z + 5)
        print("✓ 季祭坛已生成")
        
        -- 测试放入不同类型的季芯
        local test_cores = {
            SpawnPrefab("season_core_spring"),
            SpawnPrefab("season_core_summer"), 
            SpawnPrefab("season_core_autumn"),
            SpawnPrefab("season_core_winter")
        }
        
        for i, core in ipairs(test_cores) do
            if core and altar.components.trader then
                altar.components.trader.onaccept(altar, player, core)
                print("✓ 第" .. i .. "个季芯已放入祭坛")
                
                if altar.components.inspectable and altar.components.inspectable.getstatus then
                    print("  祭坛状态：" .. altar.components.inspectable.getstatus(altar))
                end
            end
        end
    end
    
    print("\n=== 季芯系统测试完成 ===")
    print("说明：")
    print("- 四种季节碎片：spring(绿), summer(橙), autumn(褐), winter(蓝)")
    print("- 通用季芯：四种碎片各1个合成，用于基础装备")
    print("- 特定季芯：同种碎片4个合成，用于制作对应炸符")
    print("- 季芯炸符：需要对应季节的特定季芯制作")
    print("- 祭坛召唤：最后放入的季芯类型决定Boss初始弱点")
end

return TestSeasonCores
