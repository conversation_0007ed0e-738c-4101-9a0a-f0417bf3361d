-- 季匠角色资源创建辅助脚本
-- 此脚本可以在游戏控制台中运行，帮助快速设置角色资源

local function CreateCharacterResources()
    print("=== 季匠角色资源设置指南 ===")
    
    print("\n当前角色资源状态：")
    print("✅ 角色代码：完全实现")
    print("✅ 角色注册：已配置")
    print("✅ 功能系统：完整工作")
    print("⏳ 视觉资源：需要创建")
    
    print("\n角色当前使用Wickerbottom模板，包括：")
    print("- 动画文件：wickerbottom.zip")
    print("- 音效文件：wickerbottom.fsb")
    print("- 基础动作：完整支持")
    
    print("\n需要创建的资源文件：")
    local required_resources = {
        "images/saveslot_portraits/season_crafter.tex/.xml",
        "images/selectscreen_portraits/season_crafter.tex/.xml", 
        "images/selectscreen_portraits/season_crafter_silho.tex/.xml",
        "bigportraits/season_crafter.tex/.xml",
        "images/map_icons/season_crafter.tex/.xml",
        "images/avatars/avatar_season_crafter.tex/.xml",
        "images/avatars/avatar_ghost_season_crafter.tex/.xml",
        "images/avatars/self_inspect_season_crafter.tex/.xml",
        "images/names_season_crafter.tex/.xml",
        "images/names_gold_season_crafter.tex/.xml"
    }
    
    for i, resource in ipairs(required_resources) do
        print("  " .. i .. ". " .. resource)
    end
    
    print("\n快速解决方案：")
    print("1. 临时方案：角色已可正常游戏，使用Wickerbottom外观")
    print("2. 复用方案：复制Wickerbottom资源并重命名")
    print("3. 改色方案：基于Wickerbottom进行颜色调整")
    print("4. 原创方案：创建全新的角色外观")
    
    print("\n推荐的创建步骤：")
    print("1. 从DST游戏目录提取Wickerbottom的资源文件")
    print("2. 使用图像编辑软件进行四季主题改色：")
    print("   - 春季：绿色调 (0.5, 1.0, 0.5)")
    print("   - 夏季：橙色调 (1.0, 0.6, 0.2)")  
    print("   - 秋季：褐色调 (0.8, 0.5, 0.2)")
    print("   - 冬季：蓝色调 (0.6, 0.8, 1.0)")
    print("3. 重命名为season_crafter对应文件名")
    print("4. 放入mod对应目录")
    print("5. 取消modmain.lua中Assets的注释")
    
    print("\n目录结构检查：")
    local mod_path = "mods/thinking/"
    print("确保以下目录存在：")
    print("  " .. mod_path .. "images/saveslot_portraits/")
    print("  " .. mod_path .. "images/selectscreen_portraits/")
    print("  " .. mod_path .. "images/map_icons/")
    print("  " .. mod_path .. "images/avatars/")
    print("  " .. mod_path .. "bigportraits/")
    
    print("\n当前可用的测试命令：")
    print("TestSeasonCrafter() - 测试角色功能")
    print("TestSeasonSwitch() - 测试季节切换")
    print("SpawnTestItems() - 生成测试物品")
    
    print("\n=== 设置完成后的验证步骤 ===")
    print("1. 重启游戏")
    print("2. 在角色选择界面查看季匠角色")
    print("3. 选择角色开始游戏")
    print("4. 运行TestSeasonCrafter()验证功能")
    
    print("\n注意：即使没有自定义资源，角色功能也完全正常！")
end

-- 检查当前角色状态
local function CheckCharacterStatus()
    local player = ThePlayer
    if not player then
        print("请先进入游戏世界")
        return
    end
    
    print("=== 当前角色状态 ===")
    print("角色prefab：", player.prefab)
    print("是否为季匠：", player.prefab == "season_crafter" and "是" or "否")
    
    if player.prefab == "season_crafter" then
        print("✅ 季匠角色已正确加载")
        print("外观：使用Wickerbottom模板")
        print("音效：wickerbottom.fsb")
        
        if player.components.season_engraving then
            print("✅ 季节刻印组件已加载")
            local season = player.components.season_engraving:GetSeason()
            print("当前季节：", season or "未知")
        else
            print("❌ 季节刻印组件未加载")
        end
        
        if player:HasTag("seasonal") then
            print("✅ 季节标签已设置")
        else
            print("❌ 季节标签未设置")
        end
        
    else
        print("当前不是季匠角色，无法测试专有功能")
    end
end

-- 生成资源模板文件信息
local function GenerateResourceTemplate()
    print("=== 资源文件模板信息 ===")
    
    local templates = {
        {
            name = "存档肖像",
            source = "images/saveslot_portraits/wickerbottom.tex/.xml",
            target = "images/saveslot_portraits/season_crafter.tex/.xml",
            size = "108x108像素",
            description = "角色存档选择界面的小肖像"
        },
        {
            name = "选择界面肖像", 
            source = "images/selectscreen_portraits/wickerbottom.tex/.xml",
            target = "images/selectscreen_portraits/season_crafter.tex/.xml",
            size = "188x284像素",
            description = "角色选择界面的大肖像"
        },
        {
            name = "选择界面剪影",
            source = "images/selectscreen_portraits/wickerbottom_silho.tex/.xml", 
            target = "images/selectscreen_portraits/season_crafter_silho.tex/.xml",
            size = "188x284像素",
            description = "角色选择界面的剪影效果"
        },
        {
            name = "检查界面大肖像",
            source = "bigportraits/wickerbottom.tex/.xml",
            target = "bigportraits/season_crafter.tex/.xml", 
            size = "622x702像素",
            description = "按Tab键检查角色时的大肖像"
        }
    }
    
    for i, template in ipairs(templates) do
        print(string.format("\n%d. %s", i, template.name))
        print("   源文件：" .. template.source)
        print("   目标：" .. template.target)
        print("   尺寸：" .. template.size)
        print("   用途：" .. template.description)
    end
    
    print("\n改色建议：")
    print("- 保持整体构图和风格")
    print("- 调整服装颜色体现四季主题")
    print("- 可添加季节元素装饰（叶片、雪花等）")
    print("- 保持角色的学者/工匠气质")
end

-- 导出函数到全局
_G.CreateCharacterResources = CreateCharacterResources
_G.CheckCharacterStatus = CheckCharacterStatus  
_G.GenerateResourceTemplate = GenerateResourceTemplate

print("角色资源创建脚本已加载")
print("使用方法：")
print("CreateCharacterResources() - 显示资源创建指南")
print("CheckCharacterStatus() - 检查当前角色状态")
print("GenerateResourceTemplate() - 生成资源模板信息")
