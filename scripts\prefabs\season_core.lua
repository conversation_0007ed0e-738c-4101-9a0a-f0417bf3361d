local assets = {}

local function MakeSeasonCore(name, season)
    local function fn()
        local inst = CreateEntity()
        inst.entity:AddTransform()
        inst.entity:AddAnimState()
        inst.entity:AddNetwork()

        MakeInventoryPhysics(inst)

        inst.AnimState:SetBank("transistor") -- 临时用电器件的外观改色思路
        inst.AnimState:SetBuild("transistor")
        inst.AnimState:PlayAnimation("idle")

        -- 根据季节设置颜色
        if season == "spring" then
            inst.AnimState:SetMultColour(0.5, 1.0, 0.5, 1.0) -- 绿色
        elseif season == "summer" then
            inst.AnimState:SetMultColour(1.0, 0.6, 0.2, 1.0) -- 橙色
        elseif season == "autumn" then
            inst.AnimState:SetMultColour(0.8, 0.5, 0.2, 1.0) -- 褐色
        elseif season == "winter" then
            inst.AnimState:SetMultColour(0.6, 0.8, 1.0, 1.0) -- 蓝色
        end

        inst:AddTag("season_core")
        if season then
            inst:AddTag("season_core_" .. season)
        end

        inst.entity:SetPristine()

        if not TheWorld.ismastersim then
            return inst
        end

        inst._season = season  -- 保存季节属性

        inst:AddComponent("inspectable")
        inst.components.inspectable.getstatus = function(inst)
            local season_names = {
                spring = "春季",
                summer = "夏季",
                autumn = "秋季",
                winter = "冬季"
            }
            if inst._season then
                return string.format("%s季芯", season_names[inst._season] or "未知")
            else
                return "混合季芯"
            end
        end

        inst:AddComponent("inventoryitem")

        return inst
    end
    return Prefab(name, fn, assets)
end

-- 创建通用季芯（混合型）
local function fn()
    local inst = CreateEntity()
    inst.entity:AddTransform()
    inst.entity:AddAnimState()
    inst.entity:AddNetwork()

    MakeInventoryPhysics(inst)

    inst.AnimState:SetBank("transistor")
    inst.AnimState:SetBuild("transistor")
    inst.AnimState:PlayAnimation("idle")

    inst:AddTag("season_core")

    inst.entity:SetPristine()

    if not TheWorld.ismastersim then
        return inst
    end

    inst:AddComponent("inspectable")
    inst.components.inspectable.getstatus = function(inst)
        return "混合季芯"
    end

    inst:AddComponent("inventoryitem")

    return inst
end

-- 返回通用季芯和四个季节特定季芯
local generic_core = Prefab("season_core", fn, assets)
local spring_core = MakeSeasonCore("season_core_spring", "spring")
local summer_core = MakeSeasonCore("season_core_summer", "summer")
local autumn_core = MakeSeasonCore("season_core_autumn", "autumn")
local winter_core = MakeSeasonCore("season_core_winter", "winter")

return generic_core, spring_core, summer_core, autumn_core, winter_core
