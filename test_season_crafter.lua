-- 季匠角色测试脚本
-- 在游戏控制台中运行此脚本来测试角色功能

local function TestSeasonCrafter()
    local player = ThePlayer
    if not player then
        print("错误：找不到玩家")
        return
    end
    
    if player.prefab ~= "season_crafter" then
        print("警告：当前角色不是季匠，测试结果可能不准确")
    end
    
    print("=== 季匠角色功能测试 ===")
    
    -- 测试基础属性
    print("基础属性测试：")
    print("- 生命值上限：", player.components.health.maxhealth, "(期望: 150)")
    print("- 饥饿值上限：", player.components.hunger.max, "(期望: 150)")
    print("- 理智值上限：", player.components.sanity.max, "(期望: 200)")
    
    -- 测试季节组件
    if player.components.season_engraving then
        print("✓ 季节刻印组件已加载")
        local current_season = player.components.season_engraving:GetSeason()
        print("- 当前季节：", current_season or "未知")
        
        -- 测试季节效果
        if current_season == "spring" then
            print("春季效果测试：")
            if player.components.waterproofer then
                print("- 防水效果：", player.components.waterproofer.effectiveness, "(期望: 0.5)")
            else
                print("- 防水效果：未激活")
            end
            
            if player.components.workmultiplier then
                print("- 工作效率加成：已激活")
            else
                print("- 工作效率加成：未激活")
            end
            
        elseif current_season == "summer" then
            print("夏季效果测试：")
            if player.components.temperature then
                print("- 夏季隔热：", player.components.temperature.inherentsummerinsulation, "(期望: 60)")
            end
            
            if player.components.locomotor then
                local speed_mult = player.components.locomotor:GetExternalSpeedMultiplier("season_eng")
                print("- 移动速度倍率：", speed_mult, "(期望: 1.2)")
            end
            
            if player.components.health then
                print("- 火焰伤害减免：", player.components.health.fire_damage_scale, "(期望: 0.5)")
            end
            
        elseif current_season == "autumn" then
            print("秋季效果测试：")
            if player.components.hunger then
                print("- 饥饿消耗倍率：约", player.components.hunger:GetRate() / TUNING.WILSON_HUNGER_RATE, "(期望: 0.5)")
            end
            
        elseif current_season == "winter" then
            print("冬季效果测试：")
            if player.components.temperature then
                print("- 冬季隔热：", player.components.temperature.inherentinsulation, "(期望: 120)")
            end
            print("- 近战减速效果：需要攻击目标才能测试")
        end
        
    else
        print("✗ 季节刻印组件未加载")
    end
    
    -- 测试标签
    if player:HasTag("seasonal") then
        print("✓ 季节标签已设置")
    else
        print("✗ 季节标签未设置")
    end
    
    print("=== 测试完成 ===")
    print("提示：切换季节后重新运行此测试以验证不同季节的效果")
end

-- 季节切换测试函数
local function TestSeasonSwitch()
    local seasons = {"spring", "summer", "autumn", "winter"}
    local current_idx = 1
    
    for i, season in ipairs(seasons) do
        if TheWorld.state.season == season then
            current_idx = i
            break
        end
    end
    
    local next_idx = (current_idx % 4) + 1
    local next_season = seasons[next_idx]
    
    print("切换季节从", TheWorld.state.season, "到", next_season)
    TheWorld:PushEvent("ms_setseason", next_season)
    
    -- 延迟测试以确保季节效果已应用
    ThePlayer:DoTaskInTime(0.5, function()
        TestSeasonCrafter()
    end)
end

-- 快速生成测试物品
local function SpawnTestItems()
    local player = ThePlayer
    if not player then return end
    
    local x, y, z = player.Transform:GetWorldPosition()
    
    -- 生成季节碎片用于测试
    local seasons = {"spring", "summer", "autumn", "winter"}
    for i, season in ipairs(seasons) do
        local shard = SpawnPrefab("season_shard_" .. season)
        if shard then
            shard.Transform:SetPosition(x + i, 0, z)
        end
    end
    
    -- 生成季芯
    local core = SpawnPrefab("season_core")
    if core then
        core.Transform:SetPosition(x, 0, z + 2)
    end
    
    -- 生成季节装备
    local blade = SpawnPrefab("season_blade")
    if blade then
        blade.Transform:SetPosition(x - 1, 0, z + 2)
    end
    
    local cloak = SpawnPrefab("climate_cloak")
    if cloak then
        cloak.Transform:SetPosition(x + 1, 0, z + 2)
    end
    
    print("测试物品已生成")
end

-- 导出测试函数到全局
_G.TestSeasonCrafter = TestSeasonCrafter
_G.TestSeasonSwitch = TestSeasonSwitch
_G.SpawnTestItems = SpawnTestItems

print("季匠测试脚本已加载")
print("使用方法：")
print("TestSeasonCrafter() - 测试角色功能")
print("TestSeasonSwitch() - 切换季节并测试")
print("SpawnTestItems() - 生成测试物品")
