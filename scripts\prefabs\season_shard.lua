local function MakeShard(name, bankbuild, season)
    local assets = {}
    local function fn()
        local inst = CreateEntity()
        inst.entity:AddTransform()
        inst.entity:AddAnimState()
        inst.entity:AddNetwork()

        MakeInventoryPhysics(inst)

        inst.AnimState:SetBank(bankbuild)
        inst.AnimState:SetBuild(bankbuild)
        inst.AnimState:PlayAnimation("idle")

        -- 根据季节设置颜色
        if season == "spring" then
            inst.AnimState:SetMultColour(0.5, 1.0, 0.5, 1.0) -- 绿色
        elseif season == "summer" then
            inst.AnimState:SetMultColour(1.0, 0.6, 0.2, 1.0) -- 橙色
        elseif season == "autumn" then
            inst.AnimState:SetMultColour(0.8, 0.5, 0.2, 1.0) -- 褐色
        elseif season == "winter" then
            inst.AnimState:SetMultColour(0.6, 0.8, 1.0, 1.0) -- 蓝色
        end

        inst:AddTag("season_orb")
        inst:AddTag("season_shard")
        if season then
            inst:AddTag("season_shard_" .. season)
        end

        inst.entity:SetPristine()
        if not TheWorld.ismastersim then
            return inst
        end

        inst._season = season  -- 保存季节属性

        inst:AddComponent("inspectable")
        inst.components.inspectable.getstatus = function(inst)
            local season_names = {
                spring = "春季",
                summer = "夏季",
                autumn = "秋季",
                winter = "冬季"
            }
            if inst._season then
                return string.format("%s碎片", season_names[inst._season] or "未知")
            else
                return "季节碎片"
            end
        end

        inst:AddComponent("inventoryitem")

        return inst
    end
    return Prefab(name, fn, assets)
end

-- 统一使用一个小物件的build作为占位，比如nightmarefuel或seeds等，后续可换改色
local spring  = MakeShard("season_shard_spring",  "nitre", "spring")
local summer  = MakeShard("season_shard_summer",  "nitre", "summer")
local autumn  = MakeShard("season_shard_autumn",  "nitre", "autumn")
local winter  = MakeShard("season_shard_winter",  "nitre", "winter")

return spring, summer, autumn, winter
