local function MakeShard(name, bankbuild)
    local assets = {}
    local function fn()
        local inst = CreateEntity()
        inst.entity:AddTransform()
        inst.entity:AddAnimState()
        inst.entity:AddNetwork()

        MakeInventoryPhysics(inst)

        inst.AnimState:SetBank(bankbuild)
        inst.AnimState:SetBuild(bankbuild)
        inst.AnimState:PlayAnimation("idle")

        inst:AddTag("season_orb")

        inst.entity:SetPristine()
        if not TheWorld.ismastersim then
            return inst
        end

        inst:AddComponent("inspectable")
        inst:AddComponent("inventoryitem")

        return inst
    end
    return Prefab(name, fn, assets)
end

-- 统一使用一个小物件的build作为占位，比如nightmarefuel或seeds等，后续可换改色
local spring  = MakeShard("season_shard_spring",  "nitre")
local summer  = MakeShard("season_shard_summer",  "nitre")
local autumn  = MakeShard("season_shard_autumn",  "nitre")
local winter  = MakeShard("season_shard_winter",  "nitre")

return spring, summer, autumn, winter
