local function MakeSigilField(name)
    local assets = {}
    local function fn()
        local inst = CreateEntity()
        inst.entity:AddTransform()
        inst.entity:AddNetwork()

        inst:AddTag("FX")
        inst.persists = false

        inst.entity:SetPristine()
        if not TheWorld.ismastersim then
            return inst
        end

        local function pulse()
            if not inst._season then return end
            local x, y, z = inst.Transform:GetWorldPosition()
            local rad = 3
            local ents = TheSim:FindEntities(x, y, z, rad, {"season_warden"})
            for _, v in ipairs(ents) do
                v:PushEvent("season_sigil", {season = inst._season})
            end
            local fx = SpawnPrefab("staff_castinglight")
            if fx then fx.Transform:SetPosition(x, 0, z); fx:DoTaskInTime(0.4, fx.Remove) end
        end

        for i=0,4 do
            inst:DoTaskInTime(0.4 * i, pulse)
        end
        inst:DoTaskInTime(2.2, inst.Remove)

        return inst
    end
    return Prefab(name, fn, assets)
end

local function MakeSigil(season, bankbuild)
    local name = "season_sigil_"..season
    local assets = {}
    local function ondeploy(inst, pt, deployer)
        local field = SpawnPrefab("sigil_field")
        if field then
            field._season = season
            field.Transform:SetPosition(pt.x, 0, pt.z)
        end
        if deployer and deployer.SoundEmitter then
            deployer.SoundEmitter:PlaySound("dontstarve/common/staff_spell")
        end
        inst:Remove()
    end

    local function fn()
        local inst = CreateEntity()
        inst.entity:AddTransform()
        inst.entity:AddAnimState()
        inst.entity:AddNetwork()

        MakeInventoryPhysics(inst)

        inst.AnimState:SetBank(bankbuild)
        inst.AnimState:SetBuild(bankbuild)
        inst.AnimState:PlayAnimation("idle")

        inst:AddTag("season_sigil")
        inst.inv_image_bg = { atlas = nil, image = nil }

        inst.entity:SetPristine()
        if not TheWorld.ismastersim then
            return inst
        end

        inst:AddComponent("inspectable")
        inst:AddComponent("inventoryitem")
        inst:AddComponent("deployable")
        inst.components.deployable.ondeploy = ondeploy
        inst.components.deployable:SetDeployMode(DEPLOYMODE.DEFAULT)
        inst.components.deployable:SetQuickDeploy(true)

        return inst
    end

    return Prefab(name, fn, assets)
end

local sigil_field = MakeSigilField("sigil_field")
local sigil_spring = MakeSigil("spring", "staffcoldlight")
local sigil_summer = MakeSigil("summer", "firestaff")
local sigil_autumn = MakeSigil("autumn", "livinglog")
local sigil_winter = MakeSigil("winter", "icestaff")

return sigil_field, sigil_spring, sigil_summer, sigil_autumn, sigil_winter
