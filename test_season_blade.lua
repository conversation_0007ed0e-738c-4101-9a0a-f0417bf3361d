-- 季节之刃功能测试脚本
-- 使用方法：在游戏控制台中执行 dofile("mods/thinking/test_season_blade.lua")

local function TestSeasonBlade()
    local player = ThePlayer
    if not player then
        print("错误：找不到玩家")
        return
    end

    print("=== 季节之刃功能测试 ===")

    -- 0. 检查玩家是否有季节刻印组件
    if not player.components.season_engraving then
        print("警告：玩家没有季节刻印组件，武器将使用世界季节")
    else
        print("✓ 玩家具有季节刻印组件")
        local player_season = player.components.season_engraving:GetSeason()
        print("- 玩家季节：" .. (player_season or "未知"))
    end

    -- 1. 生成季节之刃
    local blade = SpawnPrefab("season_blade")
    if not blade then
        print("错误：无法生成季节之刃")
        return
    end

    player.components.inventory:GiveItem(blade)
    print("✓ 季节之刃已生成并添加到背包")

    -- 2. 检查基础属性
    print("基础属性检查：")
    print("- 伤害：" .. (blade.components.weapon.damage or "未知"))
    print("- 耐久：" .. (blade.components.finiteuses.total or "未知"))

    -- 3. 测试检查功能
    if blade.components.inspectable and blade.components.inspectable.getstatus then
        print("- 检查信息：" .. blade.components.inspectable.getstatus(blade))
    end

    -- 4. 生成测试目标
    local pig = SpawnPrefab("pig")
    if pig then
        pig.Transform:SetPosition(player.Transform:GetWorldPosition())
        pig.Transform:SetPosition(pig.Transform:GetWorldPosition() + Vector3(2, 0, 0))
        print("✓ 生成测试目标：猪人")

        -- 装备武器
        player.components.inventory:Equip(blade)
        print("✓ 武器已装备")

        -- 5. 测试季节效果（如果玩家有季节刻印，测试不同季节）
        if player.components.season_engraving then
            local seasons = {"spring", "summer", "autumn", "winter"}
            for _, season in ipairs(seasons) do
                print("\n--- 测试" .. season .. "季效果 ---")

                -- 切换世界季节（这会触发季节刻印更新）
                TheWorld:PushEvent("ms_setseason", season)

                -- 等待季节刻印更新
                player.components.season_engraving:Refresh(true)

                -- 春季特殊测试：让目标变湿
                if season == "spring" and pig.components.moisture then
                    pig.components.moisture:DoDelta(50)
                    print("目标已变湿，测试湿身伤害加成")
                    print("春季爆发：主目标50点伤害，电链跳跃目标25点伤害")
                end

                -- 连续攻击3次测试爆发
                for i = 1, 3 do
                    if blade.components.weapon.onattack then
                        blade.components.weapon.onattack(blade, player, pig)
                        print("第" .. i .. "次攻击完成")

                        -- 检查武器状态
                        if blade.components.inspectable and blade.components.inspectable.getstatus then
                            print("武器状态：" .. blade.components.inspectable.getstatus(blade))
                        end
                    end
                end
            end
        else
            print("\n--- 测试当前世界季节效果 ---")
            local world_season = TheWorld.state.season or "autumn"
            print("世界季节：" .. world_season)

            -- 连续攻击3次测试爆发
            for i = 1, 3 do
                if blade.components.weapon.onattack then
                    blade.components.weapon.onattack(blade, player, pig)
                    print("第" .. i .. "次攻击完成")
                end
            end
        end
    end

    -- 6. 清理
    print("\n=== 测试完成 ===")
    print("注意：请手动清理测试生成的物品和生物")
end

-- 执行测试
TestSeasonBlade()
